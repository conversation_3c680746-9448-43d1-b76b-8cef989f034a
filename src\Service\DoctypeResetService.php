<?php

namespace App\Service;

use App\Entity\Document;
use App\Entity\User;
use App\Entity\Visa;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Service pour gérer la réinitialisation des champs lors du changement de doctype
 * Reproduit la logique de l'ancien système avec les modules Quality, Production, Purchasing, Product
 */
class DoctypeResetService
{
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;

    public function __construct(EntityManagerInterface $entityManager, LoggerInterface $logger)
    {
        $this->entityManager = $entityManager;
        $this->logger = $logger;
    }

    /**
     * Réinitialise les champs d'un document lors du changement de doctype
     *
     * @param Document $document Le document à réinitialiser
     * @param string $oldDocType L'ancien type de document
     * @param string $newDocType Le nouveau type de document
     * @param User $user L'utilisateur qui effectue le changement
     * @return array Résultat de la réinitialisation avec les champs modifiés
     */
    public function resetDocumentForDoctypeChange(Document $document, string $oldDocType, string $newDocType, User $user): array
    {
        $resetFields = [];
        $comments = [];

        // Logique spéciale pour le doctype DOC
        if ($newDocType === 'DOC') {
            $resetFields = array_merge($resetFields, $this->handleDocTypeChange($document));
            $comments[] = "[" . (new \DateTime())->format('Y-m-d H:i:s') . "] - Document type changed to DOC: Material Type set to LITERATURE";
        }

        // Logique spéciale pour sortir du doctype DOC
        if ($oldDocType === 'DOC' && $newDocType !== 'DOC') {
            $resetFields = array_merge($resetFields, $this->handleExitFromDocType($document));
            $comments[] = "[" . (new \DateTime())->format('Y-m-d H:i:s') . "] - Document type changed from DOC: Material Type reset";
        }

        // Déterminer quels modules sont concernés par le changement (sauf pour DOC)
        if ($newDocType !== 'DOC') {
            $affectedModules = $this->getAffectedModules($oldDocType, $newDocType);

            foreach ($affectedModules as $module) {
                $moduleResetResult = $this->resetFieldsForModule($document, $module, $oldDocType, $newDocType, $user);
                $resetFields = array_merge($resetFields, $moduleResetResult['fields']);
                $comments = array_merge($comments, $moduleResetResult['comments']);
            }
        }

        // Ajouter les commentaires de traçabilité
        foreach ($comments as $comment) {
            $document->addUpdate('doctype_change', $user, $comment);
        }

        // Log de l'opération
        $this->logger->info('Document doctype changed', [
            'document_id' => $document->getId(),
            'old_doctype' => $oldDocType,
            'new_doctype' => $newDocType,
            'reset_fields' => $resetFields,
            'user_id' => $user->getId()
        ]);

        return [
            'success' => true,
            'reset_fields' => $resetFields,
            'comments' => $comments
        ];
    }

    /**
     * Détermine quels modules sont affectés par le changement de doctype
     */
    private function getAffectedModules(string $oldDocType, string $newDocType): array
    {
        $modules = [];

        // Modules toujours affectés
        $modules[] = 'quality';
        $modules[] = 'product';

        // Modules selon l'ancien doctype
        if (in_array($oldDocType, ['MACH', 'MOLD', 'ASSY'])) {
            $modules[] = 'production';
        }
        if ($oldDocType === 'PUR') {
            $modules[] = 'purchasing';
        }

        // Modules selon le nouveau doctype
        if (in_array($newDocType, ['MACH', 'MOLD', 'ASSY'])) {
            if (!in_array('production', $modules)) {
                $modules[] = 'production';
            }
        }
        if ($newDocType === 'PUR') {
            if (!in_array('purchasing', $modules)) {
                $modules[] = 'purchasing';
            }
        }

        return array_unique($modules);
    }

    /**
     * Réinitialise les champs pour un module spécifique
     */
    private function resetFieldsForModule(Document $document, string $module, string $oldDocType, string $newDocType, User $user): array
    {
        $resetFields = [];
        $comments = [];
        $currentDate = new \DateTime();
        $dateString = $currentDate->format('Y-m-d H:i:s');

        switch ($module) {
            case 'quality':
                $resetFields = array_merge($resetFields, $this->resetQualityFields($document));
                $comments[] = "[$dateString] - Quality : change of supply to $newDocType";
                break;

            case 'production':
                $resetFields = array_merge($resetFields, $this->resetProductionFields($document));
                $comments[] = "[$dateString] - Prod : change of supply to $newDocType";
                break;

            case 'purchasing':
                $resetFields = array_merge($resetFields, $this->resetPurchasingFields($document));
                $comments[] = "[$dateString] - Purchasing : change of supply to $newDocType";
                break;

            case 'product':
                $resetFields = array_merge($resetFields, $this->resetProductFields($document));
                $comments[] = "[$dateString] - Product : change of supply to $newDocType";
                break;
        }

        return [
            'fields' => $resetFields,
            'comments' => $comments
        ];
    }

    /**
     * Réinitialise les champs du module Quality
     */
    private function resetQualityFields(Document $document): array
    {
        $resetFields = [];

        // Réinitialiser procType
        if ($document->getProcType() !== null) {
            $document->setProcType(null);
            $resetFields[] = 'procType';
        }

        // Réinitialiser metroTime
        if ($document->getMetroTime() !== null) {
            $document->setMetroTime(null);
            $resetFields[] = 'metroTime';
        }

        // Réinitialiser metroControl
        if ($document->getMetroControl() !== null) {
            $document->setMetroControl([]);
            $resetFields[] = 'metroControl';
        }

        // Supprimer les visas Quality, Prod, Metro
        $visasToRemove = ['visa_Quality', 'visa_prod', 'visa_Metro'];
        $resetFields = array_merge($resetFields, $this->removeVisas($document, $visasToRemove));

        return $resetFields;
    }

    /**
     * Réinitialise les champs du module Production
     */
    private function resetProductionFields(Document $document): array
    {
        $resetFields = [];

        // Réinitialiser procType
        if ($document->getProcType() !== null) {
            $document->setProcType(null);
            $resetFields[] = 'procType';
        }

        // Réinitialiser leadtime
        if ($document->getLeadtime() !== null) {
            $document->setLeadtime(0);
            $resetFields[] = 'leadtime';
        }

        // Réinitialiser mof
        if ($document->getMof() !== null) {
            $document->setMof(null);
            $resetFields[] = 'mof';
        }

        // Réinitialiser prodAgent
        if ($document->getProdAgent() !== null) {
            $document->setProdAgent(null);
            $resetFields[] = 'prodAgent';
        }

        // Réinitialiser prisDans1 et prisDans2
        if ($document->getPrisDans1() !== null) {
            $document->setPrisDans1(null);
            $resetFields[] = 'prisDans1';
        }
        if ($document->getPrisDans2() !== null) {
            $document->setPrisDans2(null);
            $resetFields[] = 'prisDans2';
        }

        // Supprimer les visas Quality et Prod
        $visasToRemove = ['visa_Quality', 'visa_prod'];
        $resetFields = array_merge($resetFields, $this->removeVisas($document, $visasToRemove));

        return $resetFields;
    }

    /**
     * Réinitialise les champs du module Purchasing
     */
    private function resetPurchasingFields(Document $document): array
    {
        $resetFields = [];

        // Réinitialiser commodityCode
        if ($document->getCommodityCode() !== null) {
            $document->setCommodityCode(null);
            $resetFields[] = 'commodityCode';
        }

        // Réinitialiser purchasingGroup
        if ($document->getPurchasingGroup() !== null) {
            $document->setPurchasingGroup(null);
            $resetFields[] = 'purchasingGroup';
        }

        // Réinitialiser procType
        if ($document->getProcType() !== null) {
            $document->setProcType(null);
            $resetFields[] = 'procType';
        }

        // Réinitialiser prisDans1 et prisDans2
        if ($document->getPrisDans1() !== null) {
            $document->setPrisDans1(null);
            $resetFields[] = 'prisDans1';
        }
        if ($document->getPrisDans2() !== null) {
            $document->setPrisDans2(null);
            $resetFields[] = 'prisDans2';
        }

        // Réinitialiser fia
        if ($document->getFia() !== null) {
            $document->setFia(null);
            $resetFields[] = 'fia';
        }

        // Réinitialiser metroTime
        if ($document->getMetroTime() !== null) {
            $document->setMetroTime(null);
            $resetFields[] = 'metroTime';
        }

        // Réinitialiser metroControl
        if ($document->getMetroControl() !== null) {
            $document->setMetroControl([]);
            $resetFields[] = 'metroControl';
        }

        // Supprimer tous les visas d'achat et qualité
        $visasToRemove = [
            'visa_Achat_Rfq', 'visa_Achat_F30', 'visa_Achat_FIA',
            'visa_Achat_RoHs_REACH', 'visa_Achat_Hts',
            'visa_Quality', 'visa_Metro'
        ];
        $resetFields = array_merge($resetFields, $this->removeVisas($document, $visasToRemove));

        return $resetFields;
    }

    /**
     * Réinitialise les champs du module Product
     */
    private function resetProductFields(Document $document): array
    {
        $resetFields = [];

        // Supprimer le visa Product
        $visasToRemove = ['visa_Produit'];
        $resetFields = array_merge($resetFields, $this->removeVisas($document, $visasToRemove));

        return $resetFields;
    }

    /**
     * Supprime les visas spécifiés du document
     */
    private function removeVisas(Document $document, array $visaNames): array
    {
        $removedVisas = [];

        foreach ($document->getVisas() as $visa) {
            if (in_array($visa->getName(), $visaNames)) {
                $document->removeVisa($visa);
                $this->entityManager->remove($visa);
                $removedVisas[] = 'visa_' . $visa->getName();
            }
        }

        return $removedVisas;
    }

    /**
     * Gère le changement vers le doctype DOC
     */
    private function handleDocTypeChange(Document $document): array
    {
        $resetFields = [];

        // Définir automatiquement le Material Type sur "LITERATURE" pour les documents DOC
        if ($document->getMatProdType() !== 'ROH') {
            $document->setMatProdType('ROH'); // ROH correspond à LITERATURE dans le mapping
            $resetFields[] = 'matProdType';
        }

        return $resetFields;
    }

    /**
     * Gère la sortie du doctype DOC
     */
    private function handleExitFromDocType(Document $document): array
    {
        $resetFields = [];

        // Réinitialiser le Material Type s'il était sur LITERATURE
        if ($document->getMatProdType() === 'ROH') {
            $document->setMatProdType(null);
            $resetFields[] = 'matProdType';
        }

        return $resetFields;
    }

    /**
     * Applique les règles spéciales selon le nouveau doctype
     */
    private function applyDoctypeSpecificRules(Document $document, string $newDocType): array
    {
        $resetFields = [];

        switch ($newDocType) {
            case 'MACH':
                // Règles spécifiques pour MACH
                // Méthode de lotissement: 1M (pas de champ direct dans le nouveau système)
                // Clé d'horizon: Z07 (pas de champ direct dans le nouveau système)
                break;

            case 'MOLD':
                // Règles spécifiques pour MOLD
                // Méthode de lotissement: 6M (pas de champ direct dans le nouveau système)
                // Clé d'horizon: Z07 (pas de champ direct dans le nouveau système)
                break;

            case 'ASSY':
                // Règles spécifiques pour ASSY
                // Méthode de lotissement: 2Z (pas de champ direct dans le nouveau système)
                // Clé d'horizon: Z07 (pas de champ direct dans le nouveau système)
                break;

            case 'PUR':
                // Règles spécifiques pour PUR
                // Clé d'horizon: Z02 (pas de champ direct dans le nouveau système)
                break;

            case 'DOC':
                // Déjà géré dans handleDocTypeChange
                break;
        }

        return $resetFields;
    }

    /**
     * Gère la logique du module Finance pour le flag Critical_Complete
     */
    private function handleFinanceLogic(Document $document, string $newDocType): array
    {
        $resetFields = [];

        // Dans le module Finance, le flag Critical_Complete est mis à 1 pour les documents DOC avec VISA_MOF
        if ($newDocType === 'DOC') {
            // Vérifier si le document a un visa MOF (équivalent à Indus dans le nouveau système)
            if ($document->hasVisa('visa_Indus')) {
                $document->setCriticalComplete(1);
                $resetFields[] = 'criticalComplete';
            }
        }

        return $resetFields;
    }
}
